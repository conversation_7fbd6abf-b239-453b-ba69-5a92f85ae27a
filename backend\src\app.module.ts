import { Module } from "@nestjs/common";
import { PassportModule } from "@nestjs/passport";
import { TagModule } from "./tag/tag.module";
import { UserModule } from "./user/user.module";
import { VoteModule } from "./vote/vote.module";
import { AuthModule } from "./auth/auth.module";
import { AdminModule } from "./admin/admin.module";
import { EmailModule } from "./email/email.module";
import { MinioModule } from "./minio/minio.module";
import { PrismaModule } from "./prisma/prisma.module";
import { VotingModule } from "./voting/voting.module";
import { RatingModule } from "./rating/rating.module";
import { CommuneModule } from "./commune/commune.module";
import { ReactorModule } from "./reactor/reactor.module";
import { AppService } from "./app.service";
import { AppController } from "./app.controller";
import { ConfigModule } from "./config/config.module";

@Module({
    imports: [
        {
            module: ConfigModule,
            global: true,
        },
        {
            module: PrismaModule,
            global: true,
        },
        PassportModule,
        AuthModule,
        UserModule,
        CommuneModule,
        VotingModule,
        VoteModule,
        EmailModule,
        MinioModule,
        AuthModule,
        ReactorModule,
        RatingModule,
        TagModule,
        AdminModule,
    ],
    controllers: [AppController],
    providers: [AppService],
})
export class AppModule {}

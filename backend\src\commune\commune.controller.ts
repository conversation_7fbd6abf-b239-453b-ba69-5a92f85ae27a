import { Common, Commune, Consts } from "@commune/api";
import {
    Controller,
    Param,
    UseGuards,
    Put,
    Delete,
    UseInterceptors,
    BadRequestException,
    ParseFilePipe,
    MaxFileSizeValidator,
    FileTypeValidator,
    UploadedFile,
    NotFoundException,
} from "@nestjs/common";
import { ZodPipe } from "src/zod";
import { getServer } from "src/acrpc";
import { CurrentUser } from "src/auth/types";
import { getError } from "src/common/errors";
import { FileInterceptor } from "@nestjs/platform-express";
import { HttpCurrentUser } from "src/auth/http/current-user.decorator";
import { HttpSessionAuthGuard } from "src/auth/http/session-auth.guard";
import { CommuneService } from "./commune.service";
import { CommuneMemberService } from "./commune-member.service";
import { CommuneInvitationService } from "./commune-invitation.service";
import { CommuneJoinRequestService } from "./commune-join-request.service";

@Controller("commune")
@UseGuards(HttpSessionAuthGuard)
export class CommuneController {
    constructor(
        private readonly communeService: CommuneService,
        private readonly communeMemberService: CommuneMemberService,
        private readonly communeInvitationService: CommuneInvitationService,
        private readonly communeJoinRequestService: CommuneJoinRequestService,
    ) {
        const acrpcServer = getServer();

        acrpcServer.register({
            commune: {
                transferHeadStatus: {
                    post: (input, metadata) =>
                        this.communeService.transferHeadStatus(
                            input,
                            metadata.user,
                        ),
                },
                list: {
                    get: (input, metadata) =>
                        this.communeService.getCommunes(input, metadata.user),
                },
                get: async (input, metadata) => {
                    const commune = await this.communeService.getCommune(
                        input,
                        metadata.user,
                    );

                    if (!commune) {
                        throw new NotFoundException(
                            ...getError("commune_not_found"),
                        );
                    }

                    return Common.parseInput(
                        Commune.GetCommuneOutputSchema,
                        commune,
                    );
                },
                post: (input, metadata) =>
                    this.communeService.createCommune(input, metadata.user),
                patch: (input, metadata) =>
                    this.communeService.updateCommune(input, metadata.user),
                delete: (input, metadata) =>
                    this.communeService.deleteCommune(input, metadata.user),
                member: {
                    list: {
                        get: (input) =>
                            this.communeMemberService.getCommuneMembers(input),
                    },
                    post: async (input, metadata) => {
                        if (!metadata.user.isAdmin) {
                            throw new NotFoundException();
                        }

                        return await this.communeMemberService.createCommuneMember(
                            input,
                        );
                    },
                    delete: (input, metadata) =>
                        this.communeMemberService.deleteCommuneMember(
                            input,
                            metadata.user,
                        ),
                },
                invitation: {
                    list: {
                        get: (input, metadata) =>
                            this.communeInvitationService.getInvitations(
                                input,
                                metadata.user,
                            ),
                    },
                    post: (input, metadata) =>
                        this.communeInvitationService.createInvitation(
                            input,
                            metadata.user,
                        ),
                    delete: (input, metadata) =>
                        this.communeInvitationService.deleteInvitation(
                            input,
                            metadata.user,
                        ),
                    accept: {
                        post: (input, metadata) =>
                            this.communeInvitationService.acceptInvitation(
                                input,
                                metadata.user,
                            ),
                    },
                    reject: {
                        post: (input, metadata) =>
                            this.communeInvitationService.rejectInvitation(
                                input,
                                metadata.user,
                            ),
                    },
                },
                joinRequest: {
                    list: {
                        get: (input, metadata) =>
                            this.communeJoinRequestService.getJoinRequests(
                                input,
                                metadata.user,
                            ),
                    },
                    post: (input, metadata) =>
                        this.communeJoinRequestService.createJoinRequest(
                            input,
                            metadata.user,
                        ),
                    delete: (input, metadata) =>
                        this.communeJoinRequestService.deleteJoinRequest(
                            input,
                            metadata.user,
                        ),
                    accept: {
                        post: (input, metadata) =>
                            this.communeJoinRequestService.acceptJoinRequest(
                                input,
                                metadata.user,
                            ),
                    },
                    reject: {
                        post: (input, metadata) =>
                            this.communeJoinRequestService.rejectJoinRequest(
                                input,
                                metadata.user,
                            ),
                    },
                },
            },
        });
    }

    // commune

    // @Post("transfer-head-status")
    // async transferHeadStatus(
    //     @Body(new ZodPipe(Commune.TransferHeadStatusInputSchema))
    //     input: Commune.TransferHeadStatusInput,
    //     @HttpCurrentUser() user: CurrentUser,
    // ) {
    //     await this.communeService.transferHeadStatus(input, user);
    // }

    // @Get()
    // async getCommunes(
    //     @Query(new ZodPipe(Common.PaginationSchema))
    //     pagination: Common.Pagination,
    //     @Query(
    //         new ZodPipe(
    //             Common.JsonStringToObject(Commune.GetCommunesInputSchema.shape),
    //         ),
    //     )
    //     input: Commune.GetCommunesInput,
    //     @HttpCurrentUser() user: CurrentUser,
    // ) {
    //     const communes = await this.communeService.getCommunes(
    //         input,
    //         pagination,
    //         user,
    //     );

    //     return Common.parseInput(Commune.GetCommunesOutputSchema, communes);
    // }

    // @Get(":id")
    // async getCommune(
    //     @Param("id", new ZodPipe(Common.id)) id: string,
    //     @HttpCurrentUser() user: CurrentUser,
    // ) {
    //     const commune = await this.communeService.getCommune(id, user);

    //     if (!commune) {
    //         throw new NotFoundException(getError("commune_not_found"));
    //     }

    //     return Common.parseInput(Commune.GetCommuneOutputSchema, commune);
    // }

    // @Post()
    // async createCommune(
    //     @Body(new ZodPipe(Commune.CreateCommuneInputSchema))
    //     input: Commune.CreateCommuneInput,
    //     @HttpCurrentUser() user: CurrentUser,
    // ) {
    //     const commune = await this.communeService.createCommune(input, user);

    //     return Common.parseInput(Common.ObjectWithIdSchema, commune);
    // }

    // @Patch(":id")
    // async updateCommune(
    //     @Param("id", new ZodPipe(Common.id)) id: string,
    //     @Body(new ZodPipe(Commune.UpdateCommuneInputSchema))
    //     input: Commune.UpdateCommuneInput,
    //     @HttpCurrentUser() user: CurrentUser,
    // ) {
    //     await this.communeService.updateCommune(id, input, user);
    // }

    @Put(":id/image")
    @UseInterceptors(FileInterceptor("image"))
    async updateCommuneImage(
        @Param("id", new ZodPipe(Common.id)) id: string,
        @HttpCurrentUser() user: CurrentUser,
        @UploadedFile(
            new ParseFilePipe({
                validators: [
                    new MaxFileSizeValidator({
                        maxSize: Consts.MAX_IMAGE_FILE_SIZE,
                    }),
                    new FileTypeValidator({
                        fileType: Consts.ALLOWED_IMAGE_FILE_TYPES.join("|"),
                    }),
                ],
            }),
        )
        file?: Express.Multer.File,
    ) {
        if (!file) {
            throw new BadRequestException("No file uploaded");
        }

        await this.communeService.updateCommuneImage(id, file, user);
    }

    @Delete(":id/image")
    async deleteCommuneImage(
        @Param("id", new ZodPipe(Common.id)) id: string,
        @HttpCurrentUser() user: CurrentUser,
    ) {
        await this.communeService.deleteCommuneImage(id, user);
    }

    // @Delete(":id")
    // async deleteCommune(
    //     @Param("id", new ZodPipe(Common.id)) id: string,
    //     @HttpCurrentUser() user: CurrentUser,
    // ) {
    //     await this.communeService.deleteCommune(id, user);
    // }

    // commune members

    // @Get(":id/member")
    // async getCommuneMembers(
    //     @Param("id", new ZodPipe(Common.id)) id: string,
    //     pagination: Common.Pagination,
    // ) {
    //     const communeMembers =
    //         await this.communeMemberService.getCommuneMembers(
    //             {
    //                 communeId: id,
    //             },
    //             pagination,
    //         );

    //     return Common.parseInput(
    //         Commune.GetCommuneMembersOutputSchema,
    //         communeMembers,
    //     );
    // }

    // @Post("member")
    // async createCommuneMember(
    //     @Body(new ZodPipe(Commune.CreateCommuneMemberInputSchema))
    //     input: Commune.CreateCommuneMemberInput,
    //     @HttpCurrentUser() user: CurrentUser,
    // ) {
    //     if (!user.isAdmin) {
    //         throw new NotFoundException();
    //     }

    //     const commune =
    //         await this.communeMemberService.createCommuneMember(input);

    //     return Common.parseInput(Common.ObjectWithIdSchema, commune);
    // }

    // @Delete("member/:id")
    // async deleteCommuneMember(
    //     @Param("id", new ZodPipe(Common.id)) id: string,
    //     @HttpCurrentUser() user: CurrentUser,
    // ) {
    //     await this.communeMemberService.deleteCommuneMember(id, user);

    //     return true;
    // }

    // commune invitations

    // @Get("invitation")
    // async getInvitations(
    //     @Query(new ZodPipe(Common.PaginationSchema))
    //     pagination: Common.Pagination,
    //     @Query(new ZodPipe(Commune.GetCommuneInvitationsInputSchema))
    //     input: Commune.GetCommuneInvitationsInput,
    //     @HttpCurrentUser() user: CurrentUser,
    // ) {
    //     const invitations = input.communeId
    //         ? await this.communeInvitationService.getInvitationsForCommune(
    //               input.communeId,
    //               pagination,
    //           )
    //         : await this.communeInvitationService.getInvitationsForUser(
    //               user.id,
    //               pagination,
    //           );

    //     return Common.parseInput(
    //         Commune.GetCommuneInvitationsOutputSchema,
    //         invitations,
    //     );
    // }

    // @Put("invitation")
    // async createInvitation(
    //     @Body(new ZodPipe(Commune.CreateCommuneInvitationInputSchema))
    //     input: Commune.CreateCommuneInvitationInput,
    //     @HttpCurrentUser() user: CurrentUser,
    // ) {
    //     const result = await this.communeInvitationService.createInvitation(
    //         input,
    //         user,
    //     );

    //     return Common.parseInput(Common.ObjectWithIdSchema, result);
    // }

    // @Delete("invitation/:id")
    // async deleteInvitation(
    //     @Param("id", new ZodPipe(Common.id)) id: string,
    //     @HttpCurrentUser() user: CurrentUser,
    // ) {
    //     await this.communeInvitationService.deleteInvitation(id, user);
    // }

    // @Post("invitation/accept/:id")
    // async acceptInvitation(
    //     @Param("id", new ZodPipe(Common.id)) id: string,
    //     @HttpCurrentUser() user: CurrentUser,
    // ) {
    //     await this.communeInvitationService.acceptInvitation(id, user);
    // }

    // @Post("invitation/reject/:id")
    // async rejectInvitation(
    //     @Param("id", new ZodPipe(Common.id)) id: string,
    //     @HttpCurrentUser() user: CurrentUser,
    // ) {
    //     await this.communeInvitationService.rejectInvitation(id, user);
    // }

    // commune join requests

    // @Get("join-request")
    // async getJoinRequests(
    //     @Query(new ZodPipe(Common.PaginationSchema))
    //     pagination: Common.Pagination,
    //     @Query(new ZodPipe(Commune.GetCommuneJoinRequestsInputSchema))
    //     input: Commune.GetCommuneJoinRequestsInput,
    //     @HttpCurrentUser() user: CurrentUser,
    // ) {
    //     const joinRequests = input.communeId
    //         ? await this.communeJoinRequestService.getJoinRequestsForCommune(
    //               input.communeId,
    //               pagination,
    //           )
    //         : await this.communeJoinRequestService.getJoinRequestsForUser(
    //               user.id,
    //               pagination,
    //           );

    //     return Common.parseInput(
    //         Commune.GetCommuneJoinRequestsOutputSchema,
    //         joinRequests,
    //     );
    // }

    // @Put("join-request")
    // async createJoinRequest(
    //     @Body(new ZodPipe(Commune.CreateCommuneJoinRequestInputSchema))
    //     input: Commune.CreateCommuneJoinRequestInput,
    //     @HttpCurrentUser() user: CurrentUser,
    // ) {
    //     const result = await this.communeJoinRequestService.createJoinRequest(
    //         input,
    //         user,
    //     );

    //     return Common.parseInput(Common.ObjectWithIdSchema, result);
    // }

    // @Delete("join-request/:id")
    // async deleteJoinRequest(
    //     @Param("id", new ZodPipe(Common.id)) id: string,
    //     @HttpCurrentUser() user: CurrentUser,
    // ) {
    //     await this.communeJoinRequestService.deleteJoinRequest(id, user);
    // }

    // @Post("join-request/accept/:id")
    // async acceptJoinRequest(
    //     @Param("id", new ZodPipe(Common.id)) id: string,
    //     @HttpCurrentUser() user: CurrentUser,
    // ) {
    //     await this.communeJoinRequestService.acceptJoinRequest(id, user);
    // }

    // @Post("join-request/reject/:id")
    // async rejectJoinRequest(
    //     @Param("id", new ZodPipe(Common.id)) id: string,
    //     @HttpCurrentUser() user: CurrentUser,
    // ) {
    //     await this.communeJoinRequestService.rejectJoinRequest(id, user);
    // }
}

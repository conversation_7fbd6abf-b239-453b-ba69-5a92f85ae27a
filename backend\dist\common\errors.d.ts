export declare const errors: {
    user_not_found: string;
    user_email_is_busy: string;
    refresh_token_invalid: string;
    otp_invalid: string;
    email_already_exists: string;
    post_not_found: string;
    must_be_admin: string;
    must_have_invite: string;
};
export type ErrorKey = keyof typeof errors;
export type ErrorData<T extends SuggestableString<ErrorKey>> = readonly [
    T,
    string
];
export declare function getError<T extends SuggestableString<ErrorKey>>(errorCode: T, additionalInfo?: string): Normalize<ErrorData<T>>;

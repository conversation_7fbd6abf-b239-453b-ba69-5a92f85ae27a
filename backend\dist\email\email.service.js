"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.EmailService = void 0;
const common_1 = require("@nestjs/common");
const nodemailer_1 = require("nodemailer");
const config_service_1 = require("../config/config.service");
const invite_text_1 = require("./invite-text");
let EmailService = class EmailService {
    constructor(configService) {
        this.configService = configService;
    }
    onModuleInit() {
        const options = {
            host: this.configService.config.email.host,
            secure: false,
            port: this.configService.config.email.port,
            auth: {
                user: this.configService.config.email.user,
                pass: this.configService.config.email.pass,
            },
            tls: {
                rejectUnauthorized: this.configService.config.email.rejectUnauthorized,
            },
        };
        this.transporter = (0, nodemailer_1.createTransport)(options);
    }
    joinAddress(sender, domain) {
        return `${sender}@${domain}`;
    }
    async send(dto) {
        if (this.configService.config.email.disableAllEmails) {
            return false;
        }
        try {
            await this.transporter.verify();
            await this.transporter.sendMail({
                from: dto.from,
                to: dto.to,
                subject: dto.subject,
                text: dto.text,
            });
            return true;
        }
        catch (e) {
            if (!this.configService.config.email.ignoreErrors) {
                throw e;
            }
            return false;
        }
    }
    async sendOtp(dto) {
        if (this.configService.config.email.disableOtpEmails) {
            return false;
        }
        return await this.send({
            from: this.joinAddress(this.configService.config.email.otpEmailSender, this.configService.config.instance.emailDomain),
            to: [dto.to],
            subject: `${this.configService.config.instance.name} - OTP`,
            text: `Your OTP is ${dto.otp}.`,
        });
    }
    async sendInvite(dto) {
        if (this.configService.config.email.disableInviteEmails) {
            return false;
        }
        return await this.send({
            from: this.joinAddress(this.configService.config.email.otpEmailSender, this.configService.config.instance.emailDomain),
            to: [dto.to],
            subject: `Commune - Invite`,
            text: (0, invite_text_1.generateInviteText)(dto.name, dto.locale),
        });
    }
};
exports.EmailService = EmailService;
exports.EmailService = EmailService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [config_service_1.ConfigService])
], EmailService);
//# sourceMappingURL=email.service.js.map
// @ts-nocheck
import type {
  LayoutServerLoad,
  LayoutServerLoadEvent,
} from "./$types";

import Negotiator from "negotiator";
import { match } from "@formatjs/intl-localematcher";
import { Common } from "@commune/api";
import { getClient } from "$lib/acrpc";

export const load = async (event: Parameters<LayoutServerLoad>[0]) => {
  const { fetcher: api } = getClient();

  const me = await api.user.me.get({ fetch: event.fetch })
    .catch(error => (console.error(error), null));

  const userLocales = getUserLocales(event);
  const preferredLocale = getPreferredLocale(event);

  return {
    me,
    preferredLocale,
    userLocales,
  };
};

function getUserLocales(event: LayoutServerLoadEvent) {
  const acceptLanguage = event.request.headers.get("accept-language");

  if (acceptLanguage) {
    const negotiatorRequest = {
      headers: {
        "accept-language": acceptLanguage,
      },
    };

    const preferredLanguages = new Negotiator(negotiatorRequest).languages();

    return preferredLanguages;
  }

  return [];
}

function getPreferredLocale(event: LayoutServerLoadEvent) {
  const userLocales = getUserLocales(event);

  return match(
    userLocales,
    Common.WebsiteLocaleSchema._def.values,
    "en",
  ) as Common.WebsiteLocale;
}

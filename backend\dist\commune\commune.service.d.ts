import { Common, Commune } from "@commune/api";
import { CurrentUser } from "src/auth/types";
import { PrismaService } from "src/prisma/prisma.service";
import { MinioService, FileInfo } from "src/minio/minio.service";
import { CommuneCore } from "./commune.core";
export declare class CommuneService {
    private readonly prisma;
    private readonly minioService;
    private readonly communeCore;
    constructor(prisma: PrismaService, minioService: MinioService, communeCore: CommuneCore);
    transferHeadStatus(input: Commune.TransferHeadStatusInput, user: CurrentUser): Promise<boolean>;
    getCommunes(input: Commune.GetCommunesInput, user: CurrentUser): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        name: {
            value: string;
            locale: "en" | "ru";
        }[];
        image: string | null;
        description: {
            value: string;
            locale: "en" | "ru";
        }[];
        headMember: {
            name: {
                value: string;
                locale: "en" | "ru";
            }[];
            image: string | null;
            actorType: "user";
            actorId: string;
        };
        memberCount: number;
        deletedAt?: Date | null | undefined;
    }[]>;
    getCommune(input: Common.ObjectWithId, user: CurrentUser): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        name: {
            value: string;
            locale: "en" | "ru";
        }[];
        image: string | null;
        description: {
            value: string;
            locale: "en" | "ru";
        }[];
        headMember: {
            name: {
                value: string;
                locale: "en" | "ru";
            }[];
            image: string | null;
            actorType: "user";
            actorId: string;
        };
        memberCount: number;
        deletedAt?: Date | null | undefined;
    } | undefined>;
    createCommune(input: Commune.CreateCommuneInput, user: CurrentUser): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        imageId: string | null;
    }>;
    updateCommune(input: Commune.UpdateCommuneInput, user: CurrentUser): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        imageId: string | null;
    }>;
    updateCommuneImage(communeId: string, file: FileInfo, user: CurrentUser): Promise<void>;
    deleteCommuneImage(communeId: string, user: CurrentUser): Promise<void>;
    deleteCommune(input: Common.ObjectWithId, user: CurrentUser): Promise<void>;
}

<script lang="ts">
  import type { Common } from "@commune/api";

  import { onMount } from "svelte";
  import { Consts } from "@commune/api";
  import { formatDate } from "$lib";
  import { getClient } from "$lib/acrpc";
  import { goto, replaceState } from "$app/navigation";
  import { Modal, LocalizedInput, LocalizedTextarea, UserPicker } from "$lib/components";

  const i18n = {
    en: {
      _page: { title: "<PERSON><PERSON> — Reactor of Commune" },
      hubs: "Hubs",
      createHub: "Create Hub",
      noHubs: "No hubs found",
      noDescription: "No description",
      head: "Head",
      errorFetchingHubs: "Failed to fetch hubs",
      errorOccurred: "An error occurred while fetching hubs",
      loadingMore: "Loading more hubs...",
      createdOn: "Created on",
      createHubTitle: "Create New Hub",
      hubName: "Hub Name",
      hubDescription: "Hub Description",
      headUserPlaceholder: "Leave empty to use current user",
      hubNamePlaceholder: "Enter hub name",
      hubDescriptionPlaceholder: "Enter hub description",
      create: "Create",
      cancel: "Cancel",
      creating: "Creating...",
      hubCreatedSuccess: "Hub created successfully!",
      errorCreatingHub: "Failed to create hub",
      required: "This field is required",
      searchPlaceholder: "Search hubs...",
    },
    ru: {
      _page: { title: "Хабы — Реактор Коммуны" },
      hubs: "Хабы",
      createHub: "Создать хаб",
      noHubs: "Хабы не найдены",
      noDescription: "Нет описания",
      head: "Глава",
      errorFetchingHubs: "Не удалось загрузить хабы",
      errorOccurred: "Произошла ошибка при загрузке хабов",
      loadingMore: "Загружаем больше хабов...",
      createdOn: "Создан",
      createHubTitle: "Создать новый хаб",
      hubName: "Название хаба",
      hubDescription: "Описание хаба",
      headUserPlaceholder: "Оставьте пустым для использования текущего пользователя",
      hubNamePlaceholder: "Введите название хаба",
      hubDescriptionPlaceholder: "Введите описание хаба",
      create: "Создать",
      cancel: "Отмена",
      creating: "Создаём...",
      hubCreatedSuccess: "Хаб успешно создан!",
      errorCreatingHub: "Не удалось создать хаб",
      required: "Это поле обязательно",
      searchPlaceholder: "Поиск хабов...",
    },
  };

  const { fetcher: api } = getClient();

  const { data } = $props();
  const { locale, toLocaleHref, getAppropriateLocalization } = $derived(data);
  const t = $derived(i18n[locale]);

  // State management
  let hubs = $state(data.hubs);
  let error = $state<string | null>(null);
  let showCreateModal = $state(false);
  let searchInputValue = $state(data.searchQuery || "");
  let searchDebounceTimeout = $state<ReturnType<typeof setTimeout> | null>(null);
  let isLoadingMore = $state(false);
  let currentPage = $state(1);
  let isHasMoreHubs = $state(data.isHasMoreHubs);
  let sentinelElement = $state<HTMLElement | null>(null);

  // Create hub modal state
  let isCreating = $state(false);
  let createError = $state<string | null>(null);
  let createSuccess = $state<string | null>(null);
  let hubName = $state<Common.Localizations>([]);
  let hubDescription = $state<Common.Localizations>([]);
  let headUserId = $state<string | null>(null);

  // API functions
  async function searchHubs(query: string, page: number = 1, append: boolean = false) {
    if (!append) isLoadingMore = true;
    error = null;

    try {
      const newHubs = await api.reactor.hub.list.get({
        pagination: { page },
        query: query.trim() || undefined,
      });

      if (append) {
        hubs = [...hubs, ...newHubs];
        currentPage = page;
      } else {
        hubs = newHubs;
        currentPage = 1;
      }

      isHasMoreHubs = newHubs.length === Consts.PAGE_SIZE;
    } catch (err) {
      error = err instanceof Error ? err.message : t.errorOccurred;
      console.error(err);
    } finally {
      isLoadingMore = false;
    }
  }

  async function loadMoreHubs() {
    if (isLoadingMore || !isHasMoreHubs) return;
    await searchHubs(searchInputValue, currentPage + 1, true);
  }

  // Event handlers
  function handleSearchChange(event: Event) {
    const target = event.target as HTMLInputElement;
    const newQuery = target.value;

    updateSearchUrl(newQuery);
    debounceSearch(newQuery);
  }

  function updateSearchUrl(query: string) {
    const url = new URL(window.location.href);
    if (query.trim()) {
      url.searchParams.set("search", encodeURIComponent(query));
    } else {
      url.searchParams.delete("search");
    }
    replaceState(url.pathname + url.search, {});
  }

  function debounceSearch(query: string) {
    if (searchDebounceTimeout) {
      clearTimeout(searchDebounceTimeout);
    }

    searchDebounceTimeout = setTimeout(() => {
      searchHubs(query);
    }, 1000);
  }

  // Modal handlers
  function openCreateModal() {
    showCreateModal = true;
    resetCreateForm();
  }

  function closeCreateModal() {
    showCreateModal = false;
  }

  function resetCreateForm() {
    hubName = [];
    hubDescription = [];
    headUserId = null;
    createError = null;
    createSuccess = null;
    isCreating = false;
  }

  function validateCreateForm(): boolean {
    if (!hubName.some((item) => item.value.trim().length > 0)) {
      createError = t.required;
      return false;
    }

    if (!hubDescription.some((item) => item.value.trim().length > 0)) {
      createError = t.required;
      return false;
    }

    return true;
  }

  async function handleCreateHub() {
    if (!validateCreateForm()) return;

    isCreating = true;
    createError = null;
    createSuccess = null;

    try {
      const { id } = await api.reactor.hub.post({
        headUserId: headUserId || data.user?.id || "",
        name: hubName,
        description: hubDescription,
      });

      createSuccess = t.hubCreatedSuccess;

      setTimeout(() => {
        goto(toLocaleHref(`/reactor/hubs/${id}`));
      }, 1500);
    } catch (err) {
      createError = err instanceof Error ? err.message : t.errorCreatingHub;
      console.error(err);
    } finally {
      isCreating = false;
    }
  }

  function truncateDescription(text: string, maxLength: number = 200): string {
    if (text.length <= maxLength) return text;
    return text.slice(0, maxLength) + "...";
  }

  function setupIntersectionObserver() {
    if (!sentinelElement) return null;

    const observer = new IntersectionObserver(
      (entries) => {
        const entry = entries[0];
        if (entry.isIntersecting && isHasMoreHubs && !isLoadingMore) {
          loadMoreHubs();
        }
      },
      {
        rootMargin: "100px",
        threshold: 0.1,
      },
    );

    observer.observe(sentinelElement);
    return observer;
  }

  // Setup intersection observer for infinite scroll
  onMount(() => {
    let observer: IntersectionObserver | null = null;

    const initObserver = () => {
      observer = setupIntersectionObserver();
    };

    // Try to setup observer immediately, or wait for element to be available
    if (sentinelElement) {
      initObserver();
    } else {
      setTimeout(initObserver, 100);
    }

    // Cleanup on component destroy
    return () => {
      observer?.disconnect();
      if (searchDebounceTimeout) {
        clearTimeout(searchDebounceTimeout);
      }
    };
  });
</script>

<svelte:head>
  <title>{t._page.title}</title>
</svelte:head>

<div class="container my-4 mb-5">
  <div class="d-flex justify-content-between align-items-center my-4 gap-3">
    <h1 class="mb-0">{t.hubs}</h1>

    <div class="d-flex align-items-center gap-3">
      <!-- Search Input -->
      <div class="search-container">
        <input
          type="text"
          class="form-control"
          placeholder={t.searchPlaceholder}
          bind:value={searchInputValue}
          oninput={handleSearchChange}
          style="min-width: 250px;"
        />
      </div>

      {#if data.user?.role === "admin"}
        <button class="btn btn-primary" onclick={openCreateModal}>
          {t.createHub}
        </button>
      {/if}
    </div>
  </div>

  {#if hubs.length === 0}
    <div class="text-center py-5">
      <p class="text-muted">{t.noHubs}</p>
    </div>
  {:else}
    <div class="row g-4">
      {#each hubs as hub (hub.id)}
        <div class="col-12">
          <div class="card shadow-sm h-100">
            <div class="row g-0 h-100">
              <!-- Hub Image -->
              <div class="col-md-3 col-lg-2">
                <div class="hub-image-container">
                  {#if hub.image}
                    <img
                      src={`/images/${hub.image}`}
                      alt={getAppropriateLocalization(hub.name) || "Hub"}
                      class="hub-image"
                    />
                  {:else}
                    <div class="hub-image-placeholder">
                      <i class="bi bi-collection fs-1 text-muted"></i>
                    </div>
                  {/if}
                </div>
              </div>

              <!-- Hub Content -->
              <div class="col-md-9 col-lg-10">
                <div class="card-body d-flex flex-column h-100 p-4">
                  <!-- Hub Name and Creation Date -->
                  <div class="d-flex justify-content-between align-items-start mb-3">
                    <h4 class="card-title mb-0 flex-grow-1">
                      <a
                        href={toLocaleHref(`/reactor/hubs/${hub.id}`)}
                        style="text-decoration: none;"
                      >
                        {getAppropriateLocalization(hub.name) || "No name?"}
                      </a>
                    </h4>
                    <small class="text-muted ms-3">
                      {t.createdOn}
                      {formatDate(hub.createdAt, locale)}
                    </small>
                  </div>

                  <!-- Hub Description -->
                  <p class="card-text text-muted mb-3 flex-grow-1">
                    {truncateDescription(
                      getAppropriateLocalization(hub.description) || t.noDescription,
                    )}
                  </p>

                  <!-- Head User Info -->
                  <div class="d-flex align-items-center">
                    <div class="me-3">
                      {#if hub.headUser.image}
                        <img
                          src={`/images/${hub.headUser.image}`}
                          alt={getAppropriateLocalization(hub.headUser.name)}
                          class="rounded-circle"
                          style="width: 48px; height: 48px; object-fit: cover;"
                        />
                      {:else}
                        <div
                          class="rounded-circle bg-secondary d-flex align-items-center justify-content-center"
                          style="width: 48px; height: 48px;"
                        >
                          <i class="bi bi-person-fill text-white"></i>
                        </div>
                      {/if}
                    </div>
                    <div>
                      <!-- <div class="small text-muted">{t.headUser}:</div> -->
                      <a
                        href={toLocaleHref(`/users/${hub.headUser.id}`)}
                        class="fw-medium"
                        style="text-decoration: none;"
                      >
                        {getAppropriateLocalization(hub.headUser.name)}
                      </a>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      {/each}
    </div>
  {/if}

  <!-- Infinite scroll sentinel element -->
  {#if isHasMoreHubs}
    <div bind:this={sentinelElement} class="text-center py-3">
      {#if isLoadingMore}
        <div class="spinner-border spinner-border-sm" role="status">
          <span class="visually-hidden">{t.loadingMore}</span>
        </div>
        <p class="text-muted mt-2 mb-0">{t.loadingMore}</p>
      {/if}
    </div>
  {/if}

  {#if error}
    <div class="alert alert-danger" role="alert">
      {error}
    </div>
  {/if}
</div>

<!-- Create Hub Modal -->
{#if data.user?.role === "admin"}
  <Modal
    show={showCreateModal}
    title={t.createHubTitle}
    onClose={closeCreateModal}
    onSubmit={handleCreateHub}
    submitText={isCreating ? t.creating : t.create}
    cancelText={t.cancel}
    submitDisabled={isCreating ||
      !hubName.some((item) => item.value.trim().length > 0) ||
      !hubDescription.some((item) => item.value.trim().length > 0)}
    isSubmitting={isCreating}
  >
    {#if createError}
      <div class="alert alert-danger mb-3">
        {createError}
      </div>
    {/if}

    {#if createSuccess}
      <div class="alert alert-success mb-3">
        {createSuccess}
      </div>
    {/if}

    <form>
      <!-- Head User Picker -->
      <UserPicker
        bind:selectedUserId={headUserId}
        {locale}
        label={t.head}
        placeholder={t.headUserPlaceholder}
      />
      <div class="form-text mb-3">
        {t.headUserPlaceholder}
      </div>

      <!-- Hub Name Input -->
      <LocalizedInput
        {locale}
        id="hub-name"
        label={t.hubName}
        placeholder={t.hubNamePlaceholder}
        required
        bind:value={hubName}
      />

      <!-- Hub Description Textarea -->
      <LocalizedTextarea
        {locale}
        id="hub-description"
        label={t.hubDescription}
        placeholder={t.hubDescriptionPlaceholder}
        rows={4}
        required
        bind:value={hubDescription}
      />
    </form>
  </Modal>
{/if}

<style>
  .hub-image-container {
    height: 200px;
    width: 100%;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f8f9fa;
  }

  .hub-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .hub-image-placeholder {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #e9ecef;
  }

  @media (max-width: 768px) {
    .hub-image-container {
      height: 150px;
    }
  }

  .card {
    transition: transform 0.2s ease-in-out;
  }

  .card:hover {
    transform: translateY(-2px);
  }

  .search-container {
    position: relative;
  }

  .search-container input {
    padding-right: 2.5rem;
  }

  @media (max-width: 768px) {
    .search-container input {
      min-width: 200px !important;
    }
  }

  @media (max-width: 576px) {
    .search-container input {
      min-width: 150px !important;
    }
  }
</style>

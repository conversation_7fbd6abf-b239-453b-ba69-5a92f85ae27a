import type { Schema } from "./core";

import {
    Common,
    Auth,
    Tag,
    Commune,
    Rating,
    Reactor,
    User,
} from "..";
import { superjsonTransformer } from "./core";

export const CACHE_CONTROL_TEN_MINUTES = "no-cache"; // "max-age=600";
export const CACHE_CONTROL_HOUR = "no-cache"; // "max-age=3600";
export const CACHE_CONTROL_IMMUTABLE = "no-cache"; // "max-age=31536000, immutable";

export const DEFAULT_CACHE_CONTROL = CACHE_CONTROL_TEN_MINUTES;

export const schema = {
    auth: {
        otp: {
            post: {
                input: Auth.SendOtpInputSchema,
                output: Auth.SendOtpOutputSchema,
                isMetadataUsed: false,
            },
        },
        signUp: {
            post: {
                input: Auth.SignupInputSchema,
                output: Auth.SuccessfulOutputSchema,
                isMetadataUsed: false,
                invalidate: ["/user/me"],
            },
        },
        signIn: {
            post: {
                input: Auth.SigninInputSchema,
                output: Auth.SuccessfulOutputSchema,
                isMetadataUsed: false,
                invalidate: ["/user/me"],
            },
        },
        signOut: {
            get: {
                input: null,
                output: null,
                isMetadataUsed: false,
                invalidate: ["/user/me"],
            },
        },
    },

    commune: {
        transferHeadStatus: {
            post: {
                input: Commune.TransferHeadStatusInputSchema,
                output: null,
                autoScopeInvalidationDepth: 2,
            },
        },
        list: {
            get: {
                input: Commune.GetCommunesInputSchema,
                output: Commune.GetCommunesOutputSchema,
                cacheControl: DEFAULT_CACHE_CONTROL,
            },
        },
        get: {
            input: Common.ObjectWithIdSchema,
            output: Commune.GetCommuneOutputSchema,
            cacheControl: DEFAULT_CACHE_CONTROL,
        },
        post: {
            input: Commune.CreateCommuneInputSchema,
            output: Common.ObjectWithIdSchema,
            autoScopeInvalidationDepth: 1,
        },
        patch: {
            input: Commune.UpdateCommuneInputSchema,
            output: null,
            autoScopeInvalidationDepth: 1,
        },
        delete: {
            input: Common.ObjectWithIdSchema,
            output: null,
            autoScopeInvalidationDepth: 1,
        },
        member: {
            list: {
                get: {
                    input: Commune.GetCommuneMembersInputSchema,
                    output: Commune.GetCommuneMembersOutputSchema,
                    cacheControl: DEFAULT_CACHE_CONTROL,
                },
            },
            post: {
                input: Commune.CreateCommuneMemberInputSchema,
                output: Common.ObjectWithIdSchema,
                autoScopeInvalidationDepth: 1,
            },
            delete: {
                input: Common.ObjectWithIdSchema,
                output: null,
                autoScopeInvalidationDepth: 1,
            },
        },
        invitation: {
            list: {
                get: {
                    input: Commune.GetCommuneInvitationsInputSchema,
                    output: Commune.GetCommuneInvitationsOutputSchema,
                    cacheControl: DEFAULT_CACHE_CONTROL,
                },
            },
            post: {
                input: Commune.CreateCommuneInvitationInputSchema,
                output: Common.ObjectWithIdSchema,
                autoScopeInvalidationDepth: 1,
            },
            delete: {
                input: Common.ObjectWithIdSchema,
                output: null,
                autoScopeInvalidationDepth: 1,
            },
            accept: {
                post: {
                    input: Common.ObjectWithIdSchema,
                    output: null,
                    // autoScopeInvalidationDepth: 2,
                    invalidate: ["/commune"],
                },
            },
            reject: {
                post: {
                    input: Common.ObjectWithIdSchema,
                    output: null,
                    // autoScopeInvalidationDepth: 2,
                    invalidate: ["/commune"],
                },
            },
        },
        joinRequest: {
            list: {
                get: {
                    input: Commune.GetCommuneJoinRequestsInputSchema,
                    output: Commune.GetCommuneJoinRequestsOutputSchema,
                    cacheControl: DEFAULT_CACHE_CONTROL,
                },
            },
            post: {
                input: Commune.CreateCommuneJoinRequestInputSchema,
                output: Common.ObjectWithIdSchema,
                autoScopeInvalidationDepth: 1,
            },
            delete: {
                input: Common.ObjectWithIdSchema,
                output: null,
                autoScopeInvalidationDepth: 1,
            },
            accept: {
                post: {
                    input: Common.ObjectWithIdSchema,
                    output: null,
                    // autoScopeInvalidationDepth: 2,
                    invalidate: ["/commune"],
                },
            },
            reject: {
                post: {
                    input: Common.ObjectWithIdSchema,
                    output: null,
                    // autoScopeInvalidationDepth: 2,
                    invalidate: ["/commune"],
                },
            },
        },
    },

    rating: {
        karma: {
            list: {
                get: {
                    input: Rating.GetKarmaPointsInputSchema,
                    output: Rating.GetKarmaPointsOutputSchema,
                    cacheControl: DEFAULT_CACHE_CONTROL,
                },
            },
            post: {
                input: Rating.SpendKarmaPointInputSchema,
                output: Common.ObjectWithIdSchema,
                autoScopeInvalidationDepth: 1,
                invalidate: ["/rating/summary"],
            },
        },
        feedback: {
            list: {
                get: {
                    input: Rating.GetUserFeedbacksInputSchema,
                    output: Rating.GetUserFeedbacksOutputSchema,
                    cacheControl: DEFAULT_CACHE_CONTROL,
                },
            },
            post: {
                input: Rating.CreateUserFeedbackInputSchema,
                output: Common.ObjectWithIdSchema,
                autoScopeInvalidationDepth: 1,
                invalidate: ["/rating/summary"],
            },
        },
        summary: {
            get: {
                input: Rating.GetUserSummaryInputSchema,
                output: Rating.GetUserSummaryOutputSchema,
                cacheControl: DEFAULT_CACHE_CONTROL,
            },
        },
    },

    reactor: {
        post: {
            list: {
                get: {
                    input: Reactor.GetPostsInputSchema,
                    output: Reactor.GetPostsOutputSchema,
                    cacheControl: DEFAULT_CACHE_CONTROL,
                },
            },
            post: {
                input: Reactor.CreatePostInputSchema,
                output: Common.ObjectWithIdSchema,
                autoScopeInvalidationDepth: 1,
            },
            patch: {
                input: Reactor.UpdatePostInputSchema,
                output: null,
                autoScopeInvalidationDepth: 1,
            },
            delete: {
                input: Reactor.DeletePostInputSchema,
                output: null,
                autoScopeInvalidationDepth: 1,
            },
            rating: {
                post: {
                    input: Reactor.UpdatePostRatingInputSchema,
                    output: Reactor.UpdatePostRatingOutputSchema,
                    autoScopeInvalidationDepth: 2,
                },
            },
            usefulness: {
                post: {
                    input: Reactor.UpdatePostUsefulnessInputSchema,
                    output: Reactor.UpdatePostUsefulnessOutputSchema,
                    autoScopeInvalidationDepth: 2,
                },
            },
            image: {
                list: {
                    get: {
                        input: Reactor.GetPostImagesInputSchema,
                        output: Reactor.GetPostImagesOutputSchema,
                        cacheControl: DEFAULT_CACHE_CONTROL,
                    },
                },
            },
        },
        comment: {
            list: {
                get: {
                    input: Reactor.GetCommentsInputSchema,
                    output: Reactor.GetCommentsOutputSchema,
                    cacheControl: DEFAULT_CACHE_CONTROL,
                },
            },
            post: {
                input: Reactor.CreateCommentInputSchema,
                output: Common.ObjectWithIdSchema,
                autoScopeInvalidationDepth: 1,
            },
            patch: {
                input: Reactor.UpdateCommentInputSchema,
                output: null,
                autoScopeInvalidationDepth: 1,
            },
            delete: {
                input: Reactor.DeleteCommentInputSchema,
                output: null,
                autoScopeInvalidationDepth: 1,
            },
            rating: {
                post: {
                    input: Reactor.UpdateCommentRatingInputSchema,
                    output: Reactor.UpdateCommentRatingOutputSchema,
                    autoScopeInvalidationDepth: 2,
                },
            },
            anonimify: {
                post: {
                    input: Reactor.AnonimifyCommentInputSchema,
                    output: null,
                    autoScopeInvalidationDepth: 2,
                },
            },
        },
        lens: {
            list: {
                get: {
                    input: null,
                    output: Reactor.GetLensesOutputSchema,
                    cacheControl: CACHE_CONTROL_IMMUTABLE,
                },
            },
            post: {
                input: Reactor.CreateLensInputSchema,
                output: Common.ObjectWithIdSchema,
                autoScopeInvalidationDepth: 1,
            },
            patch: {
                input: Reactor.UpdateLensInputSchema,
                output: null,
                autoScopeInvalidationDepth: 1,
            },
            delete: {
                input: Common.ObjectWithIdSchema,
                output: null,
                autoScopeInvalidationDepth: 1,
            },
        },
        hub: {
            list: {
                get: {
                    input: Reactor.GetHubsInputSchema,
                    output: Reactor.GetHubsOutputSchema,
                    cacheControl: DEFAULT_CACHE_CONTROL,
                },
            },
            post: {
                input: Reactor.CreateHubInputSchema,
                output: Common.ObjectWithIdSchema,
                autoScopeInvalidationDepth: 1,
            },
            patch: {
                input: Reactor.UpdateHubInputSchema,
                output: null,
                autoScopeInvalidationDepth: 1,
            },
            // delete: {
            //     input: Common.ObjectWithIdSchema,
            //     output: null,
            //     enableAutoScopeInvalidation: true,
            // },
        },
        community: {
            list: {
                get: {
                    input: Reactor.GetCommunitiesInputSchema,
                    output: Reactor.GetCommunitiesOutputSchema,
                    cacheControl: DEFAULT_CACHE_CONTROL,
                },
            },
            post: {
                input: Reactor.CreateCommunityInputSchema,
                output: Common.ObjectWithIdSchema,
                autoScopeInvalidationDepth: 1,
            },
            patch: {
                input: Reactor.UpdateCommunityInputSchema,
                output: null,
                autoScopeInvalidationDepth: 1,
            },
            // delete: {
            //     input: Common.ObjectWithIdSchema,
            //     output: null,
            //     enableAutoScopeInvalidation: true,
            // },
        },
    },

    tag: {
        list: {
            get: {
                input: Tag.GetTagsInputSchema,
                output: Tag.GetTagsOutputSchema,
                cacheControl: CACHE_CONTROL_HOUR,
            },
        },
        post: {
            input: Tag.CreateTagInputSchema,
            output: Common.ObjectWithIdSchema,
            autoScopeInvalidationDepth: 1,
        },
        patch: {
            input: Tag.UpdateTagInputSchema,
            output: null,
            autoScopeInvalidationDepth: 1,
        },
        delete: {
            input: Common.ObjectWithIdSchema,
            output: null,
            autoScopeInvalidationDepth: 1,
        },
    },

    user: {
        list: {
            get: {
                input: User.GetUsersInputSchema,
                output: User.GetUsersOutputSchema,
                cacheControl: DEFAULT_CACHE_CONTROL,
            },
        },
        me: {
            get: {
                input: null,
                output: User.GetMeOutputSchema,
                cacheControl: CACHE_CONTROL_HOUR,
            },
        },
        patch: {
            input: User.UpdateUserInputSchema,
            output: null,
            autoScopeInvalidationDepth: 1,
        },
        title: {
            list: {
                get: {
                    input: User.GetUserTitlesInputSchema,
                    output: User.GetUserTitlesOutputSchema,
                    cacheControl: DEFAULT_CACHE_CONTROL,
                },
            },
            post: {
                input: User.CreateUserTitleInputSchema,
                output: Common.ObjectWithIdSchema,
                autoScopeInvalidationDepth: 1,
            },
            patch: {
                input: User.UpdateUserTitleInputSchema,
                output: null,
                autoScopeInvalidationDepth: 1,
            },
            delete: {
                input: Common.ObjectWithIdSchema,
                output: null,
                autoScopeInvalidationDepth: 1,
            },
        },
        note: {
            get: {
                input: User.GetUserNoteInputSchema,
                output: User.GetUserNoteOutputSchema,
                cacheControl: DEFAULT_CACHE_CONTROL,
            },
            put: {
                input: User.UpdateUserNoteInputSchema,
                output: null,
                autoScopeInvalidationDepth: 1,
            },
        },
        invite: {
            list: {
                get: {
                    input: User.GetUserInvitesInputSchema,
                    output: User.GetUserInvitesOutputSchema,
                    cacheControl: DEFAULT_CACHE_CONTROL,
                },
            },
            put: {
                input: User.UpsertUserInviteInputSchema,
                output: Common.ObjectWithIdSchema,
                autoScopeInvalidationDepth: 1,
            },
            delete: {
                input: User.DeleteUserInviteInputSchema,
                output: null,
                autoScopeInvalidationDepth: 1,
            },
        },
    },
} satisfies Schema;

export const transformer = superjsonTransformer;

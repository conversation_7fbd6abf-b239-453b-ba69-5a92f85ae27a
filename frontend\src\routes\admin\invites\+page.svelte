<script lang="ts">
  import type { PageData } from "./$types";
  import type { User } from "@commune/api";

  import { Consts } from "@commune/api";
  import { getClient } from "$lib/acrpc";
  import { Modal } from "$lib/components";

  interface Props {
    data: PageData;
  }

  const { data }: Props = $props();
  const { fetcher: api } = getClient();

  let invites = $state<User.GetUserInvitesOutput>(data.invites);
  let isHasMoreInvites = $state(data.isHasMoreInvites);
  let isLoadingMore = $state(false);
  let showInviteModal = $state(false);
  let newInviteEmail = $state("");
  let isInviting = $state(false);
  let inviteError = $state<string | null>(null);
  let inviteSuccess = $state<string | null>(null);

  // Email validation regex
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

  async function loadMoreInvites() {
    if (isLoadingMore || !isHasMoreInvites) return;

    isLoadingMore = true;
    try {
      const moreInvites = await api.user.invite.list.get({
        pagination: {
          page: Math.floor(invites.length / Consts.PAGE_SIZE) + 1,
          size: Consts.PAGE_SIZE,
        },
      });

      invites = [...invites, ...moreInvites];
      isHasMoreInvites = moreInvites.length === Consts.PAGE_SIZE;
    } catch (error) {
      console.error("Error loading more invites:", error);
    } finally {
      isLoadingMore = false;
    }
  }

  async function handleInviteUser() {
    if (!newInviteEmail.trim()) {
      inviteError = "Email is required";
      return;
    }

    if (!emailRegex.test(newInviteEmail.trim())) {
      inviteError = "Please enter a valid email address";
      return;
    }

    isInviting = true;
    inviteError = null;
    inviteSuccess = null;

    try {
      const response = await api.user.invite.put({
        email: newInviteEmail.trim(),
      });

      // Check if invite already exists in the list
      const existingIndex = invites.findIndex((invite) => invite.email === newInviteEmail.trim());

      const newInviteItem = {
        id: response.id,
        email: newInviteEmail.trim(),
        isUsed: false,
      };

      if (existingIndex >= 0) {
        // Update existing invite
        invites[existingIndex] = newInviteItem;
      } else {
        // Add new invite to the beginning of the list
        invites = [newInviteItem, ...invites];
      }

      inviteSuccess = "Invitation sent successfully!";
      newInviteEmail = "";

      // Close modal after a short delay
      setTimeout(() => {
        showInviteModal = false;
        inviteSuccess = null;
      }, 1500);
    } catch (error) {
      inviteError = error instanceof Error ? error.message : "Failed to send invitation";
      console.error("Error sending invitation:", error);
    } finally {
      isInviting = false;
    }
  }

  async function handleDeleteInvite(inviteId: string) {
    if (!confirm("Are you sure you want to delete this invitation?")) {
      return;
    }

    try {
      await api.user.invite.delete({ id: inviteId });
      invites = invites.filter((invite) => invite.id !== inviteId);
    } catch (error) {
      console.error("Error deleting invitation:", error);
      alert("Failed to delete invitation");
    }
  }

  function openInviteModal() {
    showInviteModal = true;
    newInviteEmail = "";
    inviteError = null;
    inviteSuccess = null;
  }

  function closeInviteModal() {
    showInviteModal = false;
    newInviteEmail = "";
    inviteError = null;
    inviteSuccess = null;
  }

  function handleKeydown(event: KeyboardEvent) {
    if (event.key === "Enter" && !isInviting && newInviteEmail.trim()) {
      event.preventDefault();
      handleInviteUser();
    }
  }
</script>

<div class="admin-invites">
  <div
    class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom"
  >
    <h1 class="h2">User Invites</h1>
    <button type="button" class="btn btn-primary" onclick={openInviteModal}>
      <i class="bi bi-plus-circle me-1"></i>
      Send Invite
    </button>
  </div>

  {#if invites.length === 0}
    <div class="text-center py-5">
      <div class="mb-3">
        <i class="bi bi-envelope display-1 text-muted"></i>
      </div>
      <h4 class="text-muted">No invitations yet</h4>
      <p class="text-muted">Start by sending your first user invitation.</p>
      <button type="button" class="btn btn-primary" onclick={openInviteModal}>
        <i class="bi bi-plus-circle me-1"></i>
        Send First Invite
      </button>
    </div>
  {:else}
    <div class="card">
      <div class="card-header">
        <h5 class="mb-0">
          <i class="bi bi-list-ul me-2"></i>
          All Invitations ({invites.length})
        </h5>
      </div>
      <div class="card-body p-0">
        <div class="table-responsive">
          <table class="table table-hover mb-0">
            <thead class="table-light">
              <tr>
                <th scope="col">Email</th>
                <th scope="col">Status</th>
                <th scope="col">Actions</th>
              </tr>
            </thead>
            <tbody>
              {#each invites as invite (invite.email)}
                <tr class:table-secondary={invite.isUsed}>
                  <td>
                    <div class="d-flex align-items-center">
                      <i class="bi bi-envelope me-2 text-muted"></i>
                      <span class:text-muted={invite.isUsed}>{invite.email}</span>
                    </div>
                  </td>
                  <td>
                    {#if invite.isUsed}
                      <span class="badge bg-success">
                        <i class="bi bi-check-circle me-1"></i>
                        Used
                      </span>
                    {:else}
                      <span class="badge bg-warning text-dark">
                        <i class="bi bi-clock me-1"></i>
                        Pending
                      </span>
                    {/if}
                  </td>
                  <td>
                    <button
                      type="button"
                      class="btn btn-sm btn-outline-danger"
                      onclick={() => handleDeleteInvite(invite.id)}
                      aria-label="Delete invitation"
                    >
                      <i class="bi bi-trash"></i>
                    </button>
                  </td>
                </tr>
              {/each}
            </tbody>
          </table>
        </div>
      </div>
    </div>

    {#if isHasMoreInvites}
      <div class="text-center mt-4">
        <button
          type="button"
          class="btn btn-outline-primary"
          onclick={loadMoreInvites}
          disabled={isLoadingMore}
        >
          {#if isLoadingMore}
            <span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"
            ></span>
            Loading...
          {:else}
            <i class="bi bi-arrow-down-circle me-1"></i>
            Load More
          {/if}
        </button>
      </div>
    {/if}
  {/if}
</div>

<!-- Invite Modal -->
<Modal
  show={showInviteModal}
  title="Send User Invitation"
  onClose={closeInviteModal}
  onSubmit={handleInviteUser}
  submitText="Send Invite"
  cancelText="Cancel"
  submitDisabled={isInviting || !newInviteEmail.trim() || !emailRegex.test(newInviteEmail.trim())}
  isSubmitting={isInviting}
>
  {#if inviteSuccess}
    <div class="alert alert-success" role="alert">
      <i class="bi bi-check-circle me-2"></i>
      {inviteSuccess}
    </div>
  {:else}
    <form
      onsubmit={(e) => {
        e.preventDefault();
        handleInviteUser();
      }}
    >
      <div class="mb-3">
        <label for="inviteEmail" class="form-label">Email Address</label>
        <input
          type="email"
          class="form-control"
          id="inviteEmail"
          bind:value={newInviteEmail}
          placeholder="Enter email address"
          required
          onkeydown={handleKeydown}
          disabled={isInviting}
        />
        <div class="form-text">The user will receive an invitation to join the platform.</div>
      </div>

      {#if inviteError}
        <div class="alert alert-danger" role="alert">
          <i class="bi bi-exclamation-triangle me-2"></i>
          {inviteError}
        </div>
      {/if}
    </form>
  {/if}
</Modal>

<style>
  .admin-invites {
    animation: fadeIn 0.3s ease-in-out;
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .table-secondary {
    opacity: 0.7;
  }

  .table-secondary .text-muted {
    opacity: 0.8;
  }
</style>

import type { Common, Tag } from "@commune/api";
import type { CurrentUser } from "src/auth/types";
import { PrismaService } from "src/prisma/prisma.service";
export declare class TagService {
    private readonly prisma;
    private readonly logger;
    constructor(prisma: PrismaService);
    getTags(input: Tag.GetTagsInput, user: CurrentUser): Promise<{
        id: string;
        deletedAt: Date | null;
        name: {
            locale: import("@prisma/client").$Enums.Locale;
            value: string;
        }[];
    }[]>;
    createTag(input: Tag.CreateTagInput, user: CurrentUser): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
    }>;
    updateTag(input: Tag.UpdateTagInput, user: CurrentUser): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
    }>;
    deleteTag(input: Common.ObjectWithId, user: CurrentUser): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
    }>;
}
